.attachmentBubble {
    padding: 12px 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    max-width: 320px;
    min-width: 240px;
    font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
    letter-spacing: -0.01em;
    text-align: left;
    word-wrap: break-word;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.25);
    }
  }

  .othersMessage {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #212529;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.12);
  }
  
  .attachmentContent {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
  }

  .attachmentInfo {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .attachmentHeader {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .attachmentName {
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
    color: inherit;
  }

  .attachmentExtension {
    font-size: 11px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;

    .othersMessage & {
      color: rgba(33, 37, 41, 0.6);
    }
  }

  .attachmentMeta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);

    .othersMessage & {
      color: rgba(33, 37, 41, 0.5);
    }
  }

  .fileSize {
    font-weight: 500;
  }
  
  .attachmentDownload {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    color: inherit;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
      text-decoration: none;
    }

    &:active {
      transform: translateY(0);
    }

    .othersMessage & {
      background: rgba(33, 37, 41, 0.08);
      border-color: rgba(33, 37, 41, 0.15);

      &:hover {
        background: rgba(33, 37, 41, 0.12);
        border-color: rgba(33, 37, 41, 0.2);
      }
    }
  }

  .downloadIcon {
    width: 12px;
    height: 12px;
    opacity: 0.8;
  }
  
  // Enhanced file type icons
  .documentIcon, .imageIcon, .spreadsheetIcon,
  .presentationIcon, .archiveIcon, .unknownIcon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 0.5px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 8px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
      pointer-events: none;
    }

    svg {
      width: 20px;
      height: 20px;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  .documentIcon {
    background: linear-gradient(135deg, #4285F4 0%, #3367D6 100%);
    color: white;
  }

  .imageIcon {
    background: linear-gradient(135deg, #0F9D58 0%, #0D8043 100%);
    color: white;

    svg path {
      fill: #fff;
    }
  }

  .spreadsheetIcon {
    background: linear-gradient(135deg, #F4B400 0%, #E37400 100%);
    color: white;

    svg path {
      fill: #fff;
    }
  }

  .presentationIcon {
    background: linear-gradient(135deg, #DB4437 0%, #C23321 100%);
    color: white;
  }

  .archiveIcon {
    background: linear-gradient(135deg, #673AB7 0%, #512DA8 100%);
    color: white;
  }

  .unknownIcon {
    background: linear-gradient(135deg, #757575 0%, #616161 100%);
    color: white;
  }

  .imageTooltip {
    padding: 8px;
    max-width: 300px;
    background-color: rgba(0, 0, 0, 0.85);
    border-radius: 8px;
  }

  .imagePreviewContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .imagePreview {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 4px;
  }

  .imagePreviewInfo {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .imagePreviewName {
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    word-break: break-word;
  }

  .imagePreviewType {
    font-size: 12px;
    color: #ccc;
  }
