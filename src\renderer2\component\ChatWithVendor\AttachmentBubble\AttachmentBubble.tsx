import React, { useState } from 'react';
import styles from './AttachmentBubble.module.scss';
import clsx from 'clsx';
import { ReactComponent as ExcelIcon } from '../../../assets/New-images/excel-table.svg';
import { ReactComponent as ImgIcon } from '../../../assets/New-images/upload-photo.svg';
import { Tooltip, Fade } from '@mui/material';

interface AttachmentBubbleProps {
  name: string;
  extension: string;
  url: string;
  isMyMessage: boolean;
  fileSize?: number; // File size in bytes
}

// Helper function to format file size
const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '';

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const AttachmentBubble: React.FC<AttachmentBubbleProps> = ({
  name,
  extension,
  url,
  isMyMessage,
  fileSize,
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension.toLowerCase());

  // Download icon SVG
  const DownloadIcon = () => (
    <svg className={styles.downloadIcon} viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
    </svg>
  );

  // Function to determine file type icon based on extension
  const getFileTypeIcon = (extension: string) => {
    const ext = extension.toLowerCase();

    // Common document types
    if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(ext)) {
      return <div className={styles.documentIcon}>DOC</div>;
    }

    // Image types
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
      return <div className={styles.imageIcon}><ImgIcon/></div>;
    }

    // Spreadsheet types
    if (['xls', 'xlsx', 'csv'].includes(ext)) {
      return <div className={styles.spreadsheetIcon}><ExcelIcon/></div>;
    }

    // Presentation types
    if (['ppt', 'pptx'].includes(ext)) {
      return <div className={styles.presentationIcon}>PPT</div>;
    }

    // Archive types
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return <div className={styles.archiveIcon}>ZIP</div>;
    }

    // Default icon for unknown types
    return <div className={styles.unknownIcon}>FILE</div>;
  };

  // Image preview content for tooltip
  const ImagePreviewContent = () => (
    <div className={styles.imagePreviewContainer}>
      <img 
        src={url} 
        alt={name} 
        className={styles.imagePreview} 
      />
      <div className={styles.imagePreviewInfo}>
        <div className={styles.imagePreviewName}>{name}</div>
        <div className={styles.imagePreviewType}>{extension.toUpperCase()} Image</div>
      </div>
    </div>
  );

  return (
    <div
      className={clsx(
        styles.attachmentBubble,
        !isMyMessage && styles.othersMessage
      )}
    >
      {isImage ? (
        <Tooltip
          title={<ImagePreviewContent />}
          placement="top"
          arrow
          TransitionComponent={Fade}
          TransitionProps={{ timeout: 200 }}
          classes={{
            tooltip: styles.imageTooltip,
          }}
          onOpen={() => setShowPreview(true)}
          onClose={() => setShowPreview(false)}
        >
          <div className={styles.attachmentContent}>
            {getFileTypeIcon(extension)}
            <div className={styles.attachmentInfo}>
              <div className={styles.attachmentHeader}>
                <div className={styles.attachmentName}>{name}</div>
                <div className={styles.attachmentExtension}>{extension}</div>
              </div>
              {fileSize && (
                <div className={styles.attachmentMeta}>
                  <span className={styles.fileSize}>{formatFileSize(fileSize)}</span>
                </div>
              )}
            </div>
          </div>
        </Tooltip>
      ) : (
        <div className={styles.attachmentContent}>
          {getFileTypeIcon(extension)}
          <div className={styles.attachmentInfo}>
            <div className={styles.attachmentHeader}>
              <div className={styles.attachmentName}>{name}</div>
              <div className={styles.attachmentExtension}>{extension}</div>
            </div>
            {fileSize && (
              <div className={styles.attachmentMeta}>
                <span className={styles.fileSize}>{formatFileSize(fileSize)}</span>
              </div>
            )}
          </div>
        </div>
      )}
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className={styles.attachmentDownload}
      >
        <DownloadIcon />
        Download
      </a>
    </div>
  );
};

export default AttachmentBubble;
